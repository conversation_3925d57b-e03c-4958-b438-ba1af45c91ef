# GZ Log Reader

A C# console application for reading and searching through compressed (.gz) log files.

## Features

- **Read compressed log files**: Automatically decompresses and reads .gz files
- **Search functionality**: Search for specific text patterns or use regex
- **Log level filtering**: Filter by ERROR, WARN, INFO, DEBUG, etc.
- **Time range filtering**: Find logs within specific time periods
- **JSON log support**: Parse and analyze JSON-formatted logs
- **Statistics and analysis**: Get comprehensive statistics about your log files
- **Interactive mode**: Search logs interactively from the command line

## Usage

### Basic Usage

1. **Build and run the application:**
   ```bash
   dotnet build
   dotnet run
   ```

2. **Place your .gz log files in the same directory as the executable**, or modify the code to point to your log directory.

### Code Examples

#### 1. Read All Logs
```csharp
var allLogs = GzLogReader.FindLogsInGzFiles("./");
Console.WriteLine($"Found {allLogs.Count} log entries");
```

#### 2. Search for Specific Text
```csharp
// Simple text search
var errorLogs = GzLogReader.FindLogsInGzFiles("./", "ERROR", false);

// Regex search for HTTP status codes
var httpErrors = GzLogReader.FindLogsInGzFiles("./", @"\b[45]\d{2}\b", true);
```

#### 3. Filter by Log Level
```csharp
var warnLogs = GzLogReader.FindLogsByLevel("./", LogLevel.WARN);
```

#### 4. Filter by Time Range
```csharp
var yesterday = DateTime.Now.AddDays(-1);
var now = DateTime.Now;
var recentLogs = GzLogReader.FindLogsByTimeRange("./", yesterday, now);
```

#### 5. Advanced Analysis
```csharp
var analysis = AdvancedGzLogAnalyzer.AnalyzeGzLogs("./");
analysis.PrintSummary();
```

#### 6. Complex Search Criteria
```csharp
var criteria = new LogSearchCriteria
{
    SearchText = "database",
    UseRegex = false,
    StartTime = DateTime.Now.AddHours(-24),
    LogLevels = new List<LogLevel> { LogLevel.ERROR, LogLevel.WARN }
};

var results = AdvancedGzLogAnalyzer.FindLogsWithCriteria("./", criteria);
```

## Classes and Methods

### GzLogReader
- `FindLogsInGzFiles()`: Main method to search through .gz files
- `ReadGzFile()`: Read a single .gz file
- `FindLogsByLevel()`: Filter by log level
- `FindLogsByTimeRange()`: Filter by time range

### AdvancedGzLogAnalyzer
- `AnalyzeGzLogs()`: Comprehensive analysis of all .gz files
- `FindLogsWithCriteria()`: Advanced filtering with multiple criteria

### LogEntry
Represents a single log entry with:
- `FileName`: Source file name
- `FilePath`: Full path to source file
- `LineNumber`: Line number in the file
- `Content`: The actual log content
- `Timestamp`: Extracted timestamp (if found)

## Supported Log Formats

The application can handle various log formats:

1. **Standard text logs** with common timestamp formats
2. **JSON logs** with automatic field extraction
3. **Common log levels**: ERROR, WARN, INFO, DEBUG, TRACE, FATAL
4. **Multiple timestamp formats**:
   - ISO format: `2023-12-01T10:30:45`
   - Standard: `2023-12-01 10:30:45`
   - US format: `12/01/2023 10:30:45`
   - EU format: `01-12-2023 10:30:45`

## Requirements

- .NET 6.0 or later
- System.IO.Compression (included in .NET)
- System.Text.Json (included in .NET)

## Example Output

```
=== GZ Log Reader ===

Searching for .gz files in: C:\logs

1. Reading all logs from .gz files:
--------------------------------------------------
Found 1,234 total log entries

[production_json.log.30.gz:1] 2023-12-01 10:30:45 - {"level":"INFO","message":"Application started"}
[production_json.log.30.gz:2] 2023-12-01 10:30:46 - {"level":"ERROR","message":"Database connection failed"}
...

2. Searching for ERROR logs:
--------------------------------------------------
Found 45 ERROR entries
[production_json.log.30.gz:2] 2023-12-01 10:30:46 - {"level":"ERROR","message":"Database connection failed"}
...
```

## Performance Notes

- The application streams through compressed files without loading everything into memory
- Large .gz files are processed efficiently
- Regex searches may be slower than simple text searches
- Consider using specific search criteria to reduce processing time for large log sets

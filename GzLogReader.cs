using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace LogReader
{
    public class GzLogReader
    {
        /// <summary>
        /// Reads all .gz files in a directory and searches for log entries
        /// </summary>
        /// <param name="directoryPath">Directory containing .gz files</param>
        /// <param name="searchPattern">Pattern to search for (optional)</param>
        /// <param name="isRegex">Whether the search pattern is a regex</param>
        /// <returns>List of matching log entries with file information</returns>
        public static List<LogEntry> FindLogsInGzFiles(string directoryPath, string searchPattern = null, bool isRegex = false)
        {
            var results = new List<LogEntry>();
            
            // Find all .gz files in the directory
            var gzFiles = Directory.GetFiles(directoryPath, "*.gz", SearchOption.AllDirectories);
            
            Console.WriteLine($"Found {gzFiles.Length} .gz files to process...");
            
            foreach (var gzFile in gzFiles)
            {
                Console.WriteLine($"Processing: {Path.GetFileName(gzFile)}");
                
                try
                {
                    var entries = ReadGzFile(gzFile, searchPattern, isRegex);
                    results.AddRange(entries);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error processing {gzFile}: {ex.Message}");
                }
            }
            
            return results;
        }
        
        /// <summary>
        /// Reads a single .gz file and extracts log entries
        /// </summary>
        /// <param name="filePath">Path to the .gz file</param>
        /// <param name="searchPattern">Pattern to search for</param>
        /// <param name="isRegex">Whether the search pattern is a regex</param>
        /// <returns>List of matching log entries</returns>
        public static List<LogEntry> ReadGzFile(string filePath, string searchPattern = null, bool isRegex = false)
        {
            var logEntries = new List<LogEntry>();
            
            using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            using (var gzipStream = new GZipStream(fileStream, CompressionMode.Decompress))
            using (var reader = new StreamReader(gzipStream, Encoding.UTF8))
            {
                string line;
                int lineNumber = 0;
                
                while ((line = reader.ReadLine()) != null)
                {
                    lineNumber++;
                    
                    // If no search pattern, include all lines
                    if (string.IsNullOrEmpty(searchPattern))
                    {
                        logEntries.Add(new LogEntry
                        {
                            FileName = Path.GetFileName(filePath),
                            FilePath = filePath,
                            LineNumber = lineNumber,
                            Content = line,
                            Timestamp = ExtractTimestamp(line)
                        });
                    }
                    else
                    {
                        // Check if line matches the search pattern
                        bool matches = isRegex 
                            ? Regex.IsMatch(line, searchPattern, RegexOptions.IgnoreCase)
                            : line.Contains(searchPattern, StringComparison.OrdinalIgnoreCase);
                            
                        if (matches)
                        {
                            logEntries.Add(new LogEntry
                            {
                                FileName = Path.GetFileName(filePath),
                                FilePath = filePath,
                                LineNumber = lineNumber,
                                Content = line,
                                Timestamp = ExtractTimestamp(line)
                            });
                        }
                    }
                }
            }
            
            return logEntries;
        }
        
        /// <summary>
        /// Attempts to extract timestamp from a log line
        /// </summary>
        /// <param name="logLine">The log line to parse</param>
        /// <returns>Extracted DateTime or null if not found</returns>
        private static DateTime? ExtractTimestamp(string logLine)
        {
            if (string.IsNullOrEmpty(logLine))
                return null;
                
            // Common timestamp patterns
            var timestampPatterns = new[]
            {
                @"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}",           // ISO format: 2023-12-01T10:30:45
                @"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}",           // Standard: 2023-12-01 10:30:45
                @"\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}",           // US format: 12/01/2023 10:30:45
                @"\d{2}-\d{2}-\d{4} \d{2}:\d{2}:\d{2}",           // EU format: 01-12-2023 10:30:45
            };
            
            foreach (var pattern in timestampPatterns)
            {
                var match = Regex.Match(logLine, pattern);
                if (match.Success)
                {
                    if (DateTime.TryParse(match.Value, out DateTime timestamp))
                    {
                        return timestamp;
                    }
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// Searches for specific log levels (ERROR, WARN, INFO, DEBUG)
        /// </summary>
        /// <param name="directoryPath">Directory containing .gz files</param>
        /// <param name="logLevel">Log level to search for</param>
        /// <returns>List of matching log entries</returns>
        public static List<LogEntry> FindLogsByLevel(string directoryPath, LogLevel logLevel)
        {
            string pattern = logLevel.ToString();
            return FindLogsInGzFiles(directoryPath, pattern, false);
        }
        
        /// <summary>
        /// Searches for logs within a specific time range
        /// </summary>
        /// <param name="directoryPath">Directory containing .gz files</param>
        /// <param name="startTime">Start time for filtering</param>
        /// <param name="endTime">End time for filtering</param>
        /// <returns>List of matching log entries</returns>
        public static List<LogEntry> FindLogsByTimeRange(string directoryPath, DateTime startTime, DateTime endTime)
        {
            var allLogs = FindLogsInGzFiles(directoryPath);
            
            return allLogs.Where(log => 
                log.Timestamp.HasValue && 
                log.Timestamp >= startTime && 
                log.Timestamp <= endTime).ToList();
        }
    }
    
    /// <summary>
    /// Represents a log entry with metadata
    /// </summary>
    public class LogEntry
    {
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public int LineNumber { get; set; }
        public string Content { get; set; }
        public DateTime? Timestamp { get; set; }
        
        public override string ToString()
        {
            var timestamp = Timestamp?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Unknown";
            return $"[{FileName}:{LineNumber}] {timestamp} - {Content}";
        }
    }
    
    /// <summary>
    /// Common log levels
    /// </summary>
    public enum LogLevel
    {
        ERROR,
        WARN,
        INFO,
        DEBUG,
        TRACE
    }
}

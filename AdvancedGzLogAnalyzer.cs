using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace LogReader
{
    /// <summary>
    /// Advanced analyzer for .gz log files with JSON parsing and statistics
    /// </summary>
    public class AdvancedGzLogAnalyzer
    {
        /// <summary>
        /// Analyzes all .gz files and provides statistics
        /// </summary>
        /// <param name="directoryPath">Directory containing .gz files</param>
        /// <returns>Analysis results</returns>
        public static LogAnalysisResult AnalyzeGzLogs(string directoryPath)
        {
            var result = new LogAnalysisResult();
            var gzFiles = Directory.GetFiles(directoryPath, "*.gz", SearchOption.AllDirectories);
            
            Console.WriteLine($"Analyzing {gzFiles.Length} .gz files...");
            
            foreach (var gzFile in gzFiles)
            {
                try
                {
                    var fileAnalysis = AnalyzeSingleFile(gzFile);
                    result.FileAnalyses.Add(fileAnalysis);
                    result.TotalLines += fileAnalysis.LineCount;
                    result.TotalSize += new FileInfo(gzFile).Length;
                    
                    // Aggregate log levels
                    foreach (var kvp in fileAnalysis.LogLevelCounts)
                    {
                        if (result.LogLevelCounts.ContainsKey(kvp.Key))
                            result.LogLevelCounts[kvp.Key] += kvp.Value;
                        else
                            result.LogLevelCounts[kvp.Key] = kvp.Value;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error analyzing {gzFile}: {ex.Message}");
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// Analyzes a single .gz file
        /// </summary>
        /// <param name="filePath">Path to the .gz file</param>
        /// <returns>File analysis results</returns>
        private static FileAnalysisResult AnalyzeSingleFile(string filePath)
        {
            var result = new FileAnalysisResult
            {
                FileName = Path.GetFileName(filePath),
                FilePath = filePath
            };
            
            using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            using (var gzipStream = new GZipStream(fileStream, CompressionMode.Decompress))
            using (var reader = new StreamReader(gzipStream, Encoding.UTF8))
            {
                string line;
                while ((line = reader.ReadLine()) != null)
                {
                    result.LineCount++;
                    
                    // Try to parse as JSON
                    if (TryParseJsonLog(line, out var jsonLog))
                    {
                        result.JsonLogCount++;
                        
                        // Extract log level from JSON
                        if (jsonLog.ContainsKey("level"))
                        {
                            string level = jsonLog["level"].ToString().ToUpper();
                            if (result.LogLevelCounts.ContainsKey(level))
                                result.LogLevelCounts[level]++;
                            else
                                result.LogLevelCounts[level] = 1;
                        }
                        
                        // Track unique fields in JSON logs
                        foreach (var key in jsonLog.Keys)
                        {
                            if (!result.JsonFields.Contains(key))
                                result.JsonFields.Add(key);
                        }
                    }
                    else
                    {
                        // Parse traditional log format
                        var logLevel = ExtractLogLevel(line);
                        if (!string.IsNullOrEmpty(logLevel))
                        {
                            if (result.LogLevelCounts.ContainsKey(logLevel))
                                result.LogLevelCounts[logLevel]++;
                            else
                                result.LogLevelCounts[logLevel] = 1;
                        }
                    }
                    
                    // Extract timestamp
                    var timestamp = ExtractTimestamp(line);
                    if (timestamp.HasValue)
                    {
                        if (!result.EarliestTimestamp.HasValue || timestamp < result.EarliestTimestamp)
                            result.EarliestTimestamp = timestamp;
                            
                        if (!result.LatestTimestamp.HasValue || timestamp > result.LatestTimestamp)
                            result.LatestTimestamp = timestamp;
                    }
                }
            }
            
            return result;
        }
        
        /// <summary>
        /// Attempts to parse a log line as JSON
        /// </summary>
        /// <param name="logLine">The log line to parse</param>
        /// <param name="jsonData">Parsed JSON data if successful</param>
        /// <returns>True if parsing was successful</returns>
        private static bool TryParseJsonLog(string logLine, out Dictionary<string, object> jsonData)
        {
            jsonData = null;
            
            try
            {
                if (logLine.TrimStart().StartsWith("{") && logLine.TrimEnd().EndsWith("}"))
                {
                    var jsonElement = JsonSerializer.Deserialize<JsonElement>(logLine);
                    jsonData = JsonElementToDictionary(jsonElement);
                    return true;
                }
            }
            catch
            {
                // Not valid JSON
            }
            
            return false;
        }
        
        /// <summary>
        /// Converts JsonElement to Dictionary
        /// </summary>
        private static Dictionary<string, object> JsonElementToDictionary(JsonElement element)
        {
            var dict = new Dictionary<string, object>();
            
            foreach (var property in element.EnumerateObject())
            {
                dict[property.Name] = property.Value.ValueKind switch
                {
                    JsonValueKind.String => property.Value.GetString(),
                    JsonValueKind.Number => property.Value.GetDouble(),
                    JsonValueKind.True => true,
                    JsonValueKind.False => false,
                    JsonValueKind.Null => null,
                    _ => property.Value.ToString()
                };
            }
            
            return dict;
        }
        
        /// <summary>
        /// Extracts log level from a traditional log line
        /// </summary>
        private static string ExtractLogLevel(string logLine)
        {
            var logLevels = new[] { "ERROR", "WARN", "INFO", "DEBUG", "TRACE", "FATAL" };
            
            foreach (var level in logLevels)
            {
                if (logLine.Contains(level, StringComparison.OrdinalIgnoreCase))
                {
                    return level;
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// Extracts timestamp from a log line (same as in GzLogReader)
        /// </summary>
        private static DateTime? ExtractTimestamp(string logLine)
        {
            if (string.IsNullOrEmpty(logLine))
                return null;
                
            var timestampPatterns = new[]
            {
                @"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}",
                @"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}",
                @"\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}",
                @"\d{2}-\d{2}-\d{4} \d{2}:\d{2}:\d{2}",
            };
            
            foreach (var pattern in timestampPatterns)
            {
                var match = Regex.Match(logLine, pattern);
                if (match.Success)
                {
                    if (DateTime.TryParse(match.Value, out DateTime timestamp))
                    {
                        return timestamp;
                    }
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// Finds logs matching multiple criteria
        /// </summary>
        public static List<LogEntry> FindLogsWithCriteria(string directoryPath, LogSearchCriteria criteria)
        {
            var allLogs = GzLogReader.FindLogsInGzFiles(directoryPath);
            
            return allLogs.Where(log =>
            {
                // Filter by search text
                if (!string.IsNullOrEmpty(criteria.SearchText))
                {
                    bool matches = criteria.UseRegex
                        ? Regex.IsMatch(log.Content, criteria.SearchText, RegexOptions.IgnoreCase)
                        : log.Content.Contains(criteria.SearchText, StringComparison.OrdinalIgnoreCase);
                    
                    if (!matches) return false;
                }
                
                // Filter by time range
                if (criteria.StartTime.HasValue && log.Timestamp.HasValue && log.Timestamp < criteria.StartTime)
                    return false;
                    
                if (criteria.EndTime.HasValue && log.Timestamp.HasValue && log.Timestamp > criteria.EndTime)
                    return false;
                
                // Filter by log levels
                if (criteria.LogLevels?.Any() == true)
                {
                    bool hasMatchingLevel = criteria.LogLevels.Any(level =>
                        log.Content.Contains(level.ToString(), StringComparison.OrdinalIgnoreCase));
                    
                    if (!hasMatchingLevel) return false;
                }
                
                return true;
            }).ToList();
        }
    }
    
    /// <summary>
    /// Search criteria for advanced log filtering
    /// </summary>
    public class LogSearchCriteria
    {
        public string SearchText { get; set; }
        public bool UseRegex { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public List<LogLevel> LogLevels { get; set; } = new List<LogLevel>();
    }
    
    /// <summary>
    /// Results of log analysis
    /// </summary>
    public class LogAnalysisResult
    {
        public List<FileAnalysisResult> FileAnalyses { get; set; } = new List<FileAnalysisResult>();
        public long TotalLines { get; set; }
        public long TotalSize { get; set; }
        public Dictionary<string, int> LogLevelCounts { get; set; } = new Dictionary<string, int>();
        
        public void PrintSummary()
        {
            Console.WriteLine("=== Log Analysis Summary ===");
            Console.WriteLine($"Total files analyzed: {FileAnalyses.Count}");
            Console.WriteLine($"Total lines: {TotalLines:N0}");
            Console.WriteLine($"Total compressed size: {TotalSize / 1024.0 / 1024.0:F2} MB");
            Console.WriteLine();
            
            Console.WriteLine("Log level distribution:");
            foreach (var kvp in LogLevelCounts.OrderByDescending(x => x.Value))
            {
                Console.WriteLine($"  {kvp.Key}: {kvp.Value:N0}");
            }
        }
    }
    
    /// <summary>
    /// Analysis results for a single file
    /// </summary>
    public class FileAnalysisResult
    {
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long LineCount { get; set; }
        public long JsonLogCount { get; set; }
        public Dictionary<string, int> LogLevelCounts { get; set; } = new Dictionary<string, int>();
        public List<string> JsonFields { get; set; } = new List<string>();
        public DateTime? EarliestTimestamp { get; set; }
        public DateTime? LatestTimestamp { get; set; }
    }
}

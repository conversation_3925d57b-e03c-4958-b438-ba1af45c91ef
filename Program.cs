using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace LogReader
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== GZ Log File Reader ===\n");
            
            // Get the current directory (where the .gz files are located)
            string currentDirectory = Directory.GetCurrentDirectory() + "/gitlab-log";
            Console.WriteLine($"Searching for .gz files in: {currentDirectory}\n");
            
            // Example 1: Read all logs from all .gz files
            Console.WriteLine("1. Reading all logs from .gz files:");
            Console.WriteLine(new string('-', 50));
            
            var allLogs = GzLogReader.FindLogsInGzFiles(currentDirectory);
            Console.WriteLine($"Found {allLogs.Count} total log entries\n");
            
            // Show first 10 entries as sample
            var sampleLogs = allLogs.Take(10).ToList();
            foreach (var log in sampleLogs)
            {
                Console.WriteLine(log.ToString());
            }
            
            if (allLogs.Count > 10)
            {
                Console.WriteLine($"... and {allLogs.Count - 10} more entries\n");
            }
            
            // Example 2: Search for specific patterns
            Console.WriteLine("\n2. Searching for DELETE logs:");
            Console.WriteLine(new string('-', 50));
            
            var errorLogs = GzLogReader.FindLogsInGzFiles(currentDirectory, "\"method\":\"DELETE\"", false);
            Console.WriteLine($"Found {errorLogs.Count} DELETE entries");
            
            foreach (var log in errorLogs.Take(5))
            {
                Console.WriteLine(log.ToString());
            }
            
            
            // Interactive search
            Console.WriteLine("\n6. Interactive search:");
            Console.WriteLine(new string('-', 50));
            
            while (true)
            {
                Console.Write("\nEnter search term (or 'quit' to exit): ");
                string searchTerm = Console.ReadLine();
                
                if (string.IsNullOrEmpty(searchTerm) || searchTerm.ToLower() == "quit")
                    break;
                
                Console.Write("Use regex? (y/n): ");
                string useRegex = Console.ReadLine();
                bool isRegex = useRegex?.ToLower() == "y";
                
                try
                {
                    var searchResults = GzLogReader.FindLogsInGzFiles(currentDirectory, searchTerm, isRegex);
                    Console.WriteLine($"\nFound {searchResults.Count} matching entries:");
                    
                    foreach (var log in searchResults.Take(10))
                    {
                        Console.WriteLine(log.ToString());
                    }
                    
                    if (searchResults.Count > 10)
                    {
                        Console.WriteLine($"... and {searchResults.Count - 10} more entries");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error during search: {ex.Message}");
                }
            }
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}

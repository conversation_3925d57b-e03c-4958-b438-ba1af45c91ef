using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace LogReader
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== GZ Log File Reader ===\n");

            // Get the current directory (where the .gz files are located)
            string currentDirectory = Directory.GetCurrentDirectory() + "/gitlab-log";
            Console.WriteLine($"Searching for .gz files in: {currentDirectory}\n");

            // Example 1: Read all logs from all .gz files
            Console.WriteLine("1. Reading all logs from .gz files:");
            Console.WriteLine(new string('-', 50));

            var allLogs = GzLogReader.FindLogsInGzFiles(currentDirectory);
            Console.WriteLine($"Found {allLogs.Count} total log entries\n");

            // Show first 10 entries as sample
            var sampleLogs = allLogs.Take(10).ToList();
            foreach (var log in sampleLogs)
            {
                Console.WriteLine(log.ToString());
            }

            if (allLogs.Count > 10)
            {
                Console.WriteLine($"... and {allLogs.Count - 10} more entries\n");
            }

            // Example 2: Search for specific patterns
            Console.WriteLine("\n2. Searching for DELETE logs:");
            Console.WriteLine(new string('-', 50));

            var errorLogs = GzLogReader.FindLogsInGzFiles(currentDirectory, "\"method\":\"DELETE\"", false);
            Console.WriteLine($"Found {errorLogs.Count} DELETE entries");

            foreach (var log in errorLogs.Take(5))
            {
                Console.WriteLine(log.ToString());
            }


            // Interactive search
            Console.WriteLine("\n6. Interactive search:");
            Console.WriteLine(new string('-', 50));

            while (true)
            {
                Console.Write("\nEnter search term (or 'quit' to exit): ");
                string searchTerm = Console.ReadLine();

                if (string.IsNullOrEmpty(searchTerm) || searchTerm.ToLower() == "quit")
                    break;

                Console.Write("Use regex? (y/n): ");
                string useRegex = Console.ReadLine();
                bool isRegex = useRegex?.ToLower() == "y";

                try
                {
                    var searchResults = GzLogReader.FindLogsInGzFiles(currentDirectory, searchTerm, isRegex);
                    Console.WriteLine($"\nFound {searchResults.Count} matching entries:");

                    foreach (var log in searchResults.Take(10))
                    {
                        Console.WriteLine(log.ToString());
                    }

                    if (searchResults.Count > 10)
                    {
                        Console.WriteLine($"... and {searchResults.Count - 10} more entries");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error during search: {ex.Message}");
                }
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }

    /// <summary>
    /// Main class for reading and searching .gz log files
    /// </summary>
    public class GzLogReader
    {
        /// <summary>
        /// Reads all .gz files in a directory and searches for log entries
        /// </summary>
        /// <param name="directoryPath">Directory containing .gz files</param>
        /// <param name="searchPattern">Pattern to search for (optional)</param>
        /// <param name="isRegex">Whether the search pattern is a regex</param>
        /// <returns>List of matching log entries with file information</returns>
        public static List<LogEntry> FindLogsInGzFiles(string directoryPath, string searchPattern = null, bool isRegex = false)
        {
            var results = new List<LogEntry>();

            // Find all .gz files in the directory
            var gzFiles = Directory.GetFiles(directoryPath, "*.gz", SearchOption.AllDirectories);

            Console.WriteLine($"Found {gzFiles.Length} .gz files to process...");

            foreach (var gzFile in gzFiles)
            {
                Console.WriteLine($"Processing: {Path.GetFileName(gzFile)}");

                try
                {
                    var entries = ReadGzFile(gzFile, searchPattern, isRegex);
                    results.AddRange(entries);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error processing {gzFile}: {ex.Message}");
                }
            }

            return results;
        }

        /// <summary>
        /// Reads a single .gz file and extracts log entries
        /// </summary>
        /// <param name="filePath">Path to the .gz file</param>
        /// <param name="searchPattern">Pattern to search for</param>
        /// <param name="isRegex">Whether the search pattern is a regex</param>
        /// <returns>List of matching log entries</returns>
        public static List<LogEntry> ReadGzFile(string filePath, string searchPattern = null, bool isRegex = false)
        {
            var logEntries = new List<LogEntry>();

            using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            using (var gzipStream = new GZipStream(fileStream, CompressionMode.Decompress))
            using (var reader = new StreamReader(gzipStream, Encoding.UTF8))
            {
                string line;
                int lineNumber = 0;

                while ((line = reader.ReadLine()) != null)
                {
                    lineNumber++;

                    // If no search pattern, include all lines
                    if (string.IsNullOrEmpty(searchPattern))
                    {
                        logEntries.Add(new LogEntry
                        {
                            FileName = Path.GetFileName(filePath),
                            FilePath = filePath,
                            LineNumber = lineNumber,
                            Content = line,
                            Timestamp = ExtractTimestamp(line)
                        });
                    }
                    else
                    {
                        // Check if line matches the search pattern
                        bool matches = isRegex
                            ? Regex.IsMatch(line, searchPattern, RegexOptions.IgnoreCase)
                            : line.Contains(searchPattern, StringComparison.OrdinalIgnoreCase);

                        if (matches)
                        {
                            logEntries.Add(new LogEntry
                            {
                                FileName = Path.GetFileName(filePath),
                                FilePath = filePath,
                                LineNumber = lineNumber,
                                Content = line,
                                Timestamp = ExtractTimestamp(line)
                            });
                        }
                    }
                }
            }

            return logEntries;
        }

        /// <summary>
        /// Attempts to extract timestamp from a log line
        /// </summary>
        /// <param name="logLine">The log line to parse</param>
        /// <returns>Extracted DateTime or null if not found</returns>
        private static DateTime? ExtractTimestamp(string logLine)
        {
            if (string.IsNullOrEmpty(logLine))
                return null;

            // Common timestamp patterns
            var timestampPatterns = new[]
            {
                @"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}",           // ISO format: 2023-12-01T10:30:45
                @"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}",           // Standard: 2023-12-01 10:30:45
                @"\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}",           // US format: 12/01/2023 10:30:45
                @"\d{2}-\d{2}-\d{4} \d{2}:\d{2}:\d{2}",           // EU format: 01-12-2023 10:30:45
            };

            foreach (var pattern in timestampPatterns)
            {
                var match = Regex.Match(logLine, pattern);
                if (match.Success)
                {
                    if (DateTime.TryParse(match.Value, out DateTime timestamp))
                    {
                        return timestamp;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// Searches for specific log levels (ERROR, WARN, INFO, DEBUG)
        /// </summary>
        /// <param name="directoryPath">Directory containing .gz files</param>
        /// <param name="logLevel">Log level to search for</param>
        /// <returns>List of matching log entries</returns>
        public static List<LogEntry> FindLogsByLevel(string directoryPath, LogLevel logLevel)
        {
            string pattern = logLevel.ToString();
            return FindLogsInGzFiles(directoryPath, pattern, false);
        }

        /// <summary>
        /// Searches for logs within a specific time range
        /// </summary>
        /// <param name="directoryPath">Directory containing .gz files</param>
        /// <param name="startTime">Start time for filtering</param>
        /// <param name="endTime">End time for filtering</param>
        /// <returns>List of matching log entries</returns>
        public static List<LogEntry> FindLogsByTimeRange(string directoryPath, DateTime startTime, DateTime endTime)
        {
            var allLogs = FindLogsInGzFiles(directoryPath);

            return allLogs.Where(log =>
                log.Timestamp.HasValue &&
                log.Timestamp >= startTime &&
                log.Timestamp <= endTime).ToList();
        }
    }

    /// <summary>
    /// Represents a log entry with metadata
    /// </summary>
    public class LogEntry
    {
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public int LineNumber { get; set; }
        public string Content { get; set; }
        public DateTime? Timestamp { get; set; }

        public override string ToString()
        {
            var timestamp = Timestamp?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Unknown";
            return $"[{FileName}:{LineNumber}] {timestamp} - {Content}";
        }
    }

    /// <summary>
    /// Common log levels
    /// </summary>
    public enum LogLevel
    {
        ERROR,
        WARN,
        INFO,
        DEBUG,
        TRACE
    }

    /// <summary>
    /// Advanced analyzer for .gz log files with JSON parsing and statistics
    /// </summary>
    public class AdvancedGzLogAnalyzer
    {
        /// <summary>
        /// Analyzes all .gz files and provides statistics
        /// </summary>
        /// <param name="directoryPath">Directory containing .gz files</param>
        /// <returns>Analysis results</returns>
        public static LogAnalysisResult AnalyzeGzLogs(string directoryPath)
        {
            var result = new LogAnalysisResult();
            var gzFiles = Directory.GetFiles(directoryPath, "*.gz", SearchOption.AllDirectories);

            Console.WriteLine($"Analyzing {gzFiles.Length} .gz files...");

            foreach (var gzFile in gzFiles)
            {
                try
                {
                    var fileAnalysis = AnalyzeSingleFile(gzFile);
                    result.FileAnalyses.Add(fileAnalysis);
                    result.TotalLines += fileAnalysis.LineCount;
                    result.TotalSize += new FileInfo(gzFile).Length;

                    // Aggregate log levels
                    foreach (var kvp in fileAnalysis.LogLevelCounts)
                    {
                        if (result.LogLevelCounts.ContainsKey(kvp.Key))
                            result.LogLevelCounts[kvp.Key] += kvp.Value;
                        else
                            result.LogLevelCounts[kvp.Key] = kvp.Value;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error analyzing {gzFile}: {ex.Message}");
                }
            }

            return result;
        }

        /// <summary>
        /// Analyzes a single .gz file
        /// </summary>
        /// <param name="filePath">Path to the .gz file</param>
        /// <returns>File analysis results</returns>
        private static FileAnalysisResult AnalyzeSingleFile(string filePath)
        {
            var result = new FileAnalysisResult
            {
                FileName = Path.GetFileName(filePath),
                FilePath = filePath
            };

            using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            using (var gzipStream = new GZipStream(fileStream, CompressionMode.Decompress))
            using (var reader = new StreamReader(gzipStream, Encoding.UTF8))
            {
                string line;
                while ((line = reader.ReadLine()) != null)
                {
                    result.LineCount++;

                    // Try to parse as JSON
                    if (TryParseJsonLog(line, out var jsonLog))
                    {
                        result.JsonLogCount++;

                        // Extract log level from JSON
                        if (jsonLog.ContainsKey("level"))
                        {
                            string level = jsonLog["level"].ToString().ToUpper();
                            if (result.LogLevelCounts.ContainsKey(level))
                                result.LogLevelCounts[level]++;
                            else
                                result.LogLevelCounts[level] = 1;
                        }

                        // Track unique fields in JSON logs
                        foreach (var key in jsonLog.Keys)
                        {
                            if (!result.JsonFields.Contains(key))
                                result.JsonFields.Add(key);
                        }
                    }
                    else
                    {
                        // Parse traditional log format
                        var logLevel = ExtractLogLevel(line);
                        if (!string.IsNullOrEmpty(logLevel))
                        {
                            if (result.LogLevelCounts.ContainsKey(logLevel))
                                result.LogLevelCounts[logLevel]++;
                            else
                                result.LogLevelCounts[logLevel] = 1;
                        }
                    }

                    // Extract timestamp
                    var timestamp = ExtractTimestamp(line);
                    if (timestamp.HasValue)
                    {
                        if (!result.EarliestTimestamp.HasValue || timestamp < result.EarliestTimestamp)
                            result.EarliestTimestamp = timestamp;

                        if (!result.LatestTimestamp.HasValue || timestamp > result.LatestTimestamp)
                            result.LatestTimestamp = timestamp;
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Attempts to parse a log line as JSON
        /// </summary>
        /// <param name="logLine">The log line to parse</param>
        /// <param name="jsonData">Parsed JSON data if successful</param>
        /// <returns>True if parsing was successful</returns>
        private static bool TryParseJsonLog(string logLine, out Dictionary<string, object> jsonData)
        {
            jsonData = null;

            try
            {
                if (logLine.TrimStart().StartsWith("{") && logLine.TrimEnd().EndsWith("}"))
                {
                    var jsonElement = JsonSerializer.Deserialize<JsonElement>(logLine);
                    jsonData = JsonElementToDictionary(jsonElement);
                    return true;
                }
            }
            catch
            {
                // Not valid JSON
            }

            return false;
        }

        /// <summary>
        /// Converts JsonElement to Dictionary
        /// </summary>
        private static Dictionary<string, object> JsonElementToDictionary(JsonElement element)
        {
            var dict = new Dictionary<string, object>();

            foreach (var property in element.EnumerateObject())
            {
                dict[property.Name] = property.Value.ValueKind switch
                {
                    JsonValueKind.String => property.Value.GetString(),
                    JsonValueKind.Number => property.Value.GetDouble(),
                    JsonValueKind.True => true,
                    JsonValueKind.False => false,
                    JsonValueKind.Null => null,
                    _ => property.Value.ToString()
                };
            }

            return dict;
        }

        /// <summary>
        /// Extracts log level from a traditional log line
        /// </summary>
        private static string ExtractLogLevel(string logLine)
        {
            var logLevels = new[] { "ERROR", "WARN", "INFO", "DEBUG", "TRACE", "FATAL" };

            foreach (var level in logLevels)
            {
                if (logLine.Contains(level, StringComparison.OrdinalIgnoreCase))
                {
                    return level;
                }
            }

            return null;
        }

        /// <summary>
        /// Extracts timestamp from a log line (same as in GzLogReader)
        /// </summary>
        private static DateTime? ExtractTimestamp(string logLine)
        {
            if (string.IsNullOrEmpty(logLine))
                return null;

            var timestampPatterns = new[]
            {
                @"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}",
                @"\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}",
                @"\d{2}/\d{2}/\d{4} \d{2}:\d{2}:\d{2}",
                @"\d{2}-\d{2}-\d{4} \d{2}:\d{2}:\d{2}",
            };

            foreach (var pattern in timestampPatterns)
            {
                var match = Regex.Match(logLine, pattern);
                if (match.Success)
                {
                    if (DateTime.TryParse(match.Value, out DateTime timestamp))
                    {
                        return timestamp;
                    }
                }
            }

            return null;
        }

        /// <summary>
        /// Finds logs matching multiple criteria
        /// </summary>
        public static List<LogEntry> FindLogsWithCriteria(string directoryPath, LogSearchCriteria criteria)
        {
            var allLogs = GzLogReader.FindLogsInGzFiles(directoryPath);

            return allLogs.Where(log =>
            {
                // Filter by search text
                if (!string.IsNullOrEmpty(criteria.SearchText))
                {
                    bool matches = criteria.UseRegex
                        ? Regex.IsMatch(log.Content, criteria.SearchText, RegexOptions.IgnoreCase)
                        : log.Content.Contains(criteria.SearchText, StringComparison.OrdinalIgnoreCase);

                    if (!matches) return false;
                }

                // Filter by time range
                if (criteria.StartTime.HasValue && log.Timestamp.HasValue && log.Timestamp < criteria.StartTime)
                    return false;

                if (criteria.EndTime.HasValue && log.Timestamp.HasValue && log.Timestamp > criteria.EndTime)
                    return false;

                // Filter by log levels
                if (criteria.LogLevels?.Any() == true)
                {
                    bool hasMatchingLevel = criteria.LogLevels.Any(level =>
                        log.Content.Contains(level.ToString(), StringComparison.OrdinalIgnoreCase));

                    if (!hasMatchingLevel) return false;
                }

                return true;
            }).ToList();
        }
    }

    /// <summary>
    /// Search criteria for advanced log filtering
    /// </summary>
    public class LogSearchCriteria
    {
        public string SearchText { get; set; }
        public bool UseRegex { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public List<LogLevel> LogLevels { get; set; } = new List<LogLevel>();
    }

    /// <summary>
    /// Results of log analysis
    /// </summary>
    public class LogAnalysisResult
    {
        public List<FileAnalysisResult> FileAnalyses { get; set; } = new List<FileAnalysisResult>();
        public long TotalLines { get; set; }
        public long TotalSize { get; set; }
        public Dictionary<string, int> LogLevelCounts { get; set; } = new Dictionary<string, int>();

        public void PrintSummary()
        {
            Console.WriteLine("=== Log Analysis Summary ===");
            Console.WriteLine($"Total files analyzed: {FileAnalyses.Count}");
            Console.WriteLine($"Total lines: {TotalLines:N0}");
            Console.WriteLine($"Total compressed size: {TotalSize / 1024.0 / 1024.0:F2} MB");
            Console.WriteLine();

            Console.WriteLine("Log level distribution:");
            foreach (var kvp in LogLevelCounts.OrderByDescending(x => x.Value))
            {
                Console.WriteLine($"  {kvp.Key}: {kvp.Value:N0}");
            }
        }
    }

    /// <summary>
    /// Analysis results for a single file
    /// </summary>
    public class FileAnalysisResult
    {
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long LineCount { get; set; }
        public long JsonLogCount { get; set; }
        public Dictionary<string, int> LogLevelCounts { get; set; } = new Dictionary<string, int>();
        public List<string> JsonFields { get; set; } = new List<string>();
        public DateTime? EarliestTimestamp { get; set; }
        public DateTime? LatestTimestamp { get; set; }
    }
}
